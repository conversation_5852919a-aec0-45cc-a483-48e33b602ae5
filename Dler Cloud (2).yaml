port: 8888
socks-port: 8889
mixed-port: 8899
allow-lan: true
mode: Rule
log-level: info
external-controller: '127.0.0.1:6170'
secret: dler
experimental:
    ignore-resolve-fail: true
cfw-latency-url: 'http://cp.cloudflare.com/generate_204'
cfw-latency-timeout: 3000
cfw-latency-type: 1
cfw-conn-break-strategy: true
clash-for-android:
    ui-subtitle-pattern: '[一-龥]{2,4}'
url-rewrite:
    - '^https?:\/\/(www.)?(g|google)\.cn https://www.google.com 302'
    - '^https?:\/\/(ditu|maps).google\.cn https://maps.google.com 302'
proxies:
    - { name: '🇭🇰 香港 HA [01]', type: ss, server: ha.hk.1.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 HA [02]', type: ss, server: ha.hk.2.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 1052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇭🇰 香港 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 1052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇭🇰 香港 IEPL [03] [Std]', type: ss, server: iepl.std.7034.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [04] [Std]', type: ss, server: iepl.std.7531.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [05] [Pro]', type: ss, server: iepl.pro.6312.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [06] [Pro]', type: ss, server: iepl.pro.6580.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [07] [Ultra]', type: ss, server: iepl.ultra.1634.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [08] [Ultra]', type: ss, server: iepl.ultra.7553.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [09] [AC]', type: ss, server: hk-1.ac.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [10] [AC]', type: ss, server: hk-2.ac.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [11] [AC]', type: ss, server: hk-3.ac.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇭🇰 香港 IEPL [12] [AC]', type: ss, server: hk-4.ac.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇨🇳 台湾 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 2052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇨🇳 台湾 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 2052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇨🇳 台湾 IEPL [03] [Std]', type: ss, server: iepl.std.9931.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇨🇳 台湾 IEPL [04] [Pro]', type: ss, server: pro.tw.4.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇨🇳 台湾 IEPL [05] [Ultra]', type: ss, server: iepl.ultra.7683.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇨🇳 台湾 IEPL [06] [AC]', type: ss, server: tw-1.ac.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇸🇬 新加坡 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 3052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇸🇬 新加坡 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 3052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇸🇬 新加坡 IEPL [03] [Std]', type: ss, server: iepl.std.3846.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇸🇬 新加坡 IEPL [04] [Pro]', type: ss, server: pro.sg.4.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇸🇬 新加坡 IEPL [05] [Ultra]', type: ss, server: iepl.ultra.6981.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇸🇬 新加坡 IEPL [06] [AC]', type: ss, server: sg-1.ac.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇯🇵 日本 HA [01]', type: ss, server: ha.jp.1.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇯🇵 日本 HA [02]', type: ss, server: ha.jp.2.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇯🇵 日本 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 4052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇯🇵 日本 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 4052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇯🇵 日本 IEPL [03] [Plus]', type: ss, server: iepl.plus.6123.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇯🇵 日本 IEPL [04] [Plus]', type: ss, server: iepl.plus.6676.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇯🇵 日本 IEPL [05] [Max]', type: ss, server: jp-1.max.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇯🇵 日本 IEPL [06] [BGP]', type: ss, server: jp-1.bgp.0tk8a3a1q4t94dler.com, port: 41908, cipher: aes-128-gcm, password: kri57qZ8gxcY, udp: true }
    - { name: '🇰🇷 韩国 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 5052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇰🇷 韩国 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 5052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇰🇷 韩国 IEPL [03] [Ultra]', type: ss, server: pop.ultra.6341.0tk8a3a1q4t94dler.com, port: 5052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇰🇷 韩国 IEPL [04] [Plus]', type: ss, server: pop.plus.3123.0tk8a3a1q4t94dler.com, port: 5052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇰🇷 韩国 IEPL [05] [AC]', type: ss, server: hk-pop-1.ac.0tk8a3a1q4t94dler.com, port: 5052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇺🇸 美国 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 6052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇺🇸 美国 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 6052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇺🇸 美国 IEPL [03] [Ultra]', type: ss, server: pop.ultra.6341.0tk8a3a1q4t94dler.com, port: 4052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇺🇸 美国 IEPL [04] [Plus]', type: ss, server: pop.plus.3123.0tk8a3a1q4t94dler.com, port: 4052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇺🇸 美国 IEPL [05] [AC]', type: ss, server: hk-pop-1.ac.0tk8a3a1q4t94dler.com, port: 4052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇷🇺 俄罗斯 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 2062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇷🇺 俄罗斯 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 2062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇨🇦 加拿大 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 3062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇨🇦 加拿大 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 3062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇮🇳 印度 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 4062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇮🇳 印度 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 4062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇹🇷 土耳其 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 5062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇹🇷 土耳其 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 5062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇳🇬 尼日利亚 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 5072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇳🇬 尼日利亚 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 5072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇧🇷 巴西 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 6062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇧🇷 巴西 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 6062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇩🇪 德国 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 7052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇩🇪 德国 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 7052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇩🇪 德国 IEPL [03] [Ultra]', type: ss, server: pop.ultra.6341.0tk8a3a1q4t94dler.com, port: 6052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇩🇪 德国 IEPL [04] [Plus]', type: ss, server: pop.plus.3123.0tk8a3a1q4t94dler.com, port: 6052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇩🇪 德国 IEPL [05] [AC]', type: ss, server: hk-pop-1.ac.0tk8a3a1q4t94dler.com, port: 6052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇫🇷 法国 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 7062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇫🇷 法国 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 7062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇹🇭 泰国 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 8062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇹🇭 泰国 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 8062, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇦🇺 澳大利亚 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 2072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇦🇺 澳大利亚 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 2072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇬🇧 英国 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 8052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇬🇧 英国 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 8052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇬🇧 英国 IEPL [03] [Ultra]', type: ss, server: pop.ultra.6341.0tk8a3a1q4t94dler.com, port: 7052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇬🇧 英国 IEPL [04] [Plus]', type: ss, server: pop.plus.3123.0tk8a3a1q4t94dler.com, port: 7052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇬🇧 英国 IEPL [05] [AC]', type: ss, server: hk-pop-1.ac.0tk8a3a1q4t94dler.com, port: 7052, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇵🇭 菲律宾 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 3072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇵🇭 菲律宾 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 3072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇦🇷 阿根廷 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 4072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇦🇷 阿根廷 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 4072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇲🇾 马来西亚 IEPL [01] [Air]', type: ss, server: pop.air.1586.0tk8a3a1q4t94dler.com, port: 6072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
    - { name: '🇲🇾 马来西亚 IEPL [02] [Air]', type: ss, server: pop.air.6478.0tk8a3a1q4t94dler.com, port: 6072, cipher: aes-128-gcm, password: VXPipi29nxMO, udp: false, plugin: obfs, plugin-opts: { mode: http, host: 0ac4e1c38ee4a2446b934d86af812518143395.taobao.com } }
proxy-groups:
    - { name: Proxy, type: select, proxies: ['Auto - UrlTest', DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Domestic, type: select, proxies: [DIRECT, Proxy, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Others, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: AdBlock, type: select, proxies: [REJECT, DIRECT, Proxy] }
    - { name: HTTPDNS, type: select, proxies: [DIRECT, REJECT, Proxy] }
    - { name: Netflix, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'Disney Plus', type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: YouTube, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Max, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Spotify, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'CN Mainland TV', type: select, proxies: [DIRECT, Proxy, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'Asian TV', type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'Global TV', type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Apple, type: select, proxies: [DIRECT, Proxy, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'Apple TV', type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Telegram, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'Google FCM', type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Crypto, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Discord, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Microsoft, type: select, proxies: [DIRECT, Proxy, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'AI Suite', type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: PayPal, type: select, proxies: [DIRECT, Proxy, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Scholar, type: select, proxies: [DIRECT, Proxy, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Speedtest, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: Steam, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: TikTok, type: select, proxies: [Proxy, DIRECT, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: miHoYo, type: select, proxies: [DIRECT, Proxy, '🇭🇰 香港 HA [01]', '🇭🇰 香港 HA [02]', '🇭🇰 香港 IEPL [01] [Air]', '🇭🇰 香港 IEPL [02] [Air]', '🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [01] [Air]', '🇨🇳 台湾 IEPL [02] [Air]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [01] [Air]', '🇸🇬 新加坡 IEPL [02] [Air]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 HA [01]', '🇯🇵 日本 HA [02]', '🇯🇵 日本 IEPL [01] [Air]', '🇯🇵 日本 IEPL [02] [Air]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]', '🇰🇷 韩国 IEPL [01] [Air]', '🇰🇷 韩国 IEPL [02] [Air]', '🇰🇷 韩国 IEPL [03] [Ultra]', '🇰🇷 韩国 IEPL [04] [Plus]', '🇰🇷 韩国 IEPL [05] [AC]', '🇺🇸 美国 IEPL [01] [Air]', '🇺🇸 美国 IEPL [02] [Air]', '🇺🇸 美国 IEPL [03] [Ultra]', '🇺🇸 美国 IEPL [04] [Plus]', '🇺🇸 美国 IEPL [05] [AC]', '🇷🇺 俄罗斯 IEPL [01] [Air]', '🇷🇺 俄罗斯 IEPL [02] [Air]', '🇨🇦 加拿大 IEPL [01] [Air]', '🇨🇦 加拿大 IEPL [02] [Air]', '🇮🇳 印度 IEPL [01] [Air]', '🇮🇳 印度 IEPL [02] [Air]', '🇹🇷 土耳其 IEPL [01] [Air]', '🇹🇷 土耳其 IEPL [02] [Air]', '🇳🇬 尼日利亚 IEPL [01] [Air]', '🇳🇬 尼日利亚 IEPL [02] [Air]', '🇧🇷 巴西 IEPL [01] [Air]', '🇧🇷 巴西 IEPL [02] [Air]', '🇩🇪 德国 IEPL [01] [Air]', '🇩🇪 德国 IEPL [02] [Air]', '🇩🇪 德国 IEPL [03] [Ultra]', '🇩🇪 德国 IEPL [04] [Plus]', '🇩🇪 德国 IEPL [05] [AC]', '🇫🇷 法国 IEPL [01] [Air]', '🇫🇷 法国 IEPL [02] [Air]', '🇹🇭 泰国 IEPL [01] [Air]', '🇹🇭 泰国 IEPL [02] [Air]', '🇦🇺 澳大利亚 IEPL [01] [Air]', '🇦🇺 澳大利亚 IEPL [02] [Air]', '🇬🇧 英国 IEPL [01] [Air]', '🇬🇧 英国 IEPL [02] [Air]', '🇬🇧 英国 IEPL [03] [Ultra]', '🇬🇧 英国 IEPL [04] [Plus]', '🇬🇧 英国 IEPL [05] [AC]', '🇵🇭 菲律宾 IEPL [01] [Air]', '🇵🇭 菲律宾 IEPL [02] [Air]', '🇦🇷 阿根廷 IEPL [01] [Air]', '🇦🇷 阿根廷 IEPL [02] [Air]', '🇲🇾 马来西亚 IEPL [01] [Air]', '🇲🇾 马来西亚 IEPL [02] [Air]'] }
    - { name: 'Auto - UrlTest', type: url-test, proxies: ['🇭🇰 香港 IEPL [03] [Std]', '🇭🇰 香港 IEPL [04] [Std]', '🇭🇰 香港 IEPL [05] [Pro]', '🇭🇰 香港 IEPL [06] [Pro]', '🇭🇰 香港 IEPL [07] [Ultra]', '🇭🇰 香港 IEPL [08] [Ultra]', '🇭🇰 香港 IEPL [09] [AC]', '🇭🇰 香港 IEPL [10] [AC]', '🇭🇰 香港 IEPL [11] [AC]', '🇭🇰 香港 IEPL [12] [AC]', '🇨🇳 台湾 IEPL [03] [Std]', '🇨🇳 台湾 IEPL [04] [Pro]', '🇨🇳 台湾 IEPL [05] [Ultra]', '🇨🇳 台湾 IEPL [06] [AC]', '🇸🇬 新加坡 IEPL [03] [Std]', '🇸🇬 新加坡 IEPL [04] [Pro]', '🇸🇬 新加坡 IEPL [05] [Ultra]', '🇸🇬 新加坡 IEPL [06] [AC]', '🇯🇵 日本 IEPL [03] [Plus]', '🇯🇵 日本 IEPL [04] [Plus]', '🇯🇵 日本 IEPL [05] [Max]', '🇯🇵 日本 IEPL [06] [BGP]'], url: 'http://cp.cloudflare.com/generate_204', interval: '3600' }
rules:
    - 'RULE-SET,AdBlock,AdBlock'
    - 'RULE-SET,HTTPDNS,HTTPDNS'
    - 'RULE-SET,Special,DIRECT'
    - 'RULE-SET,Netflix,Netflix'
    - 'RULE-SET,Disney Plus,Disney Plus'
    - 'RULE-SET,YouTube,YouTube'
    - 'RULE-SET,Max,Max'
    - 'RULE-SET,Spotify,Spotify'
    - 'RULE-SET,Bilibili,CN Mainland TV'
    - 'RULE-SET,IQ,CN Mainland TV'
    - 'RULE-SET,IQIYI,CN Mainland TV'
    - 'RULE-SET,Letv,CN Mainland TV'
    - 'RULE-SET,Netease Music,CN Mainland TV'
    - 'RULE-SET,Tencent Video,CN Mainland TV'
    - 'RULE-SET,WeTV,CN Mainland TV'
    - 'RULE-SET,Youku,CN Mainland TV'
    - 'RULE-SET,Abema TV,Asian TV'
    - 'RULE-SET,Bahamut,Asian TV'
    - 'RULE-SET,DMM,Asian TV'
    - 'RULE-SET,Fox+,Asian TV'
    - 'RULE-SET,Hulu Japan,Asian TV'
    - 'RULE-SET,Japonx,Asian TV'
    - 'RULE-SET,JOOX,Asian TV'
    - 'RULE-SET,KKBOX,Asian TV'
    - 'RULE-SET,KKTV,Asian TV'
    - 'RULE-SET,Line TV,Asian TV'
    - 'RULE-SET,myTV SUPER,Asian TV'
    - 'RULE-SET,Niconico,Asian TV'
    - 'RULE-SET,ViuTV,Asian TV'
    - 'RULE-SET,ABC,Global TV'
    - 'RULE-SET,Amazon,Global TV'
    - 'RULE-SET,BBC iPlayer,Global TV'
    - 'RULE-SET,DAZN,Global TV'
    - 'RULE-SET,Discovery Plus,Global TV'
    - 'RULE-SET,encoreTVB,Global TV'
    - 'RULE-SET,F1 TV,Global TV'
    - 'RULE-SET,Fox Now,Global TV'
    - 'RULE-SET,Hulu,Global TV'
    - 'RULE-SET,Pandora,Global TV'
    - 'RULE-SET,PBS,Global TV'
    - 'RULE-SET,Pornhub,Global TV'
    - 'RULE-SET,Soundcloud,Global TV'
    - 'RULE-SET,Telegram,Telegram'
    - 'RULE-SET,Crypto,Crypto'
    - 'RULE-SET,Discord,Discord'
    - 'RULE-SET,Google FCM,Google FCM'
    - 'RULE-SET,Microsoft,Microsoft'
    - 'RULE-SET,AI Suite,AI Suite'
    - 'RULE-SET,PayPal,PayPal'
    - 'RULE-SET,Scholar,Scholar'
    - 'RULE-SET,Speedtest,Speedtest'
    - 'RULE-SET,Steam,Steam'
    - 'RULE-SET,TikTok,TikTok'
    - 'RULE-SET,Apple Music,Apple TV'
    - 'RULE-SET,Apple News,Apple TV'
    - 'RULE-SET,Apple TV,Apple TV'
    - 'RULE-SET,Apple,Apple'
    - 'RULE-SET,miHoYo,miHoYo'
    - 'RULE-SET,PROXY,Proxy'
    - 'RULE-SET,Domestic,Domestic'
    - 'RULE-SET,Domestic IPs,Domestic'
    - 'RULE-SET,LAN,DIRECT'
    - 'GEOIP,CN,Domestic'
    - 'MATCH,Others'
script:
    code: "def main(ctx,metadata):\n    ruleset_action = {\n        'AdBlock': 'AdBlock',\n        'HTTPDNS': 'HTTPDNS',\n        'Special': 'DIRECT',\n        'Netflix': 'Netflix',\n        'Disney Plus': 'Disney Plus',\n        'YouTube': 'YouTube',\n        'Max': 'Max',\n        'Spotify': 'Spotify',\n        'Bilibili': 'CN Mainland TV',\n        'IQ': 'CN Mainland TV',\n        'IQIYI': 'CN Mainland TV',\n        'Letv': 'CN Mainland TV',\n        'Netease Music': 'CN Mainland TV',\n        'Tencent Video': 'CN Mainland TV',\n        'Youku': 'CN Mainland TV',\n        'WeTV': 'CN Mainland TV',\n        'Abema TV': 'Asian TV',\n        'Bahamut': 'Asian TV',\n        'DMM': 'Asian TV',\n        'Fox+': 'Asian TV',\n        'Hulu Japan': 'Asian TV',\n        'Japonx': 'Asian TV',\n        'JOOX': 'Asian TV',\n        'KKBOX': 'Asian TV',\n        'KKTV': 'Asian TV',\n        'Line TV': 'Asian TV',\n        'myTV SUPER': 'Asian TV',\n        'Niconico': 'Asian TV',\n        'ViuTV': 'Asian TV',\n        'ABC': 'Global TV',\n        'Amazon': 'Global TV',\n        'BBC iPlayer': 'Global TV',\n        'DAZN': 'Global TV',\n        'Discovery Plus': 'Global TV',\n        'encoreTVB': 'Global TV',\n        'F1 TV': 'Global TV',\n        'Fox Now': 'Global TV',\n        'Hulu': 'Global TV',\n        'Pandora': 'Global TV',\n        'PBS': 'Global TV',\n        'Pornhub': 'Global TV',\n        'Soundcloud': 'Global TV',\n        'Apple Music': 'Apple TV',\n        'Apple News': 'Apple TV',\n        'Apple TV': 'Apple TV',\n        'Apple': 'Apple',\n        'Telegram': 'Telegram',\n        'Crypto': 'Crypto',\n        'Discord': 'Discord',\n        'Google FCM': 'Google FCM',\n        'Microsoft': 'Microsoft',\n        'AI Suite': 'AI Suite',\n        'PayPal': 'PayPal',\n        'Scholar': 'Scholar',\n        'Speedtest': 'Speedtest',\n        'Steam': 'Steam',\n        'TikTok': 'TikTok',\n        'miHoYo': 'miHoYo',\n        'PROXY': 'Proxy',\n        'Domestic': 'Domestic',\n        'Domestic IPs': 'Domestic',\n        'LAN': 'DIRECT'\n      }\n\n    port = int(metadata['dst_port'])\n\n    if metadata['network'] == 'UDP' and port == 443:\n        ctx.log('[Script] matched QUIC traffic use reject')\n        return 'REJECT'\n\n    port_list = [21,22,23,53,80,123,143,194,443,465,587,853,993,995,998,2052,2053,2082,2083,2086,2095,2096,3389,5222,5228,5229,5230,8080,8443,8880,8888,8889]\n    if port not in port_list:\n        ctx.log('[Script] not common port use direct')\n        return 'DIRECT'\n\n    if metadata['dst_ip'] == '':\n        metadata['dst_ip'] = ctx.resolve_ip(metadata['host'])\n\n    for ruleset in ruleset_action:\n        if ctx.rule_providers[ruleset].match(metadata):\n            return ruleset_action[ruleset]\n\n    if metadata['dst_ip'] != '':\n        code = ctx.geoip(metadata['dst_ip'])\n        if code == 'CN':\n            ctx.log('[Script] Geoip CN')\n            return 'Domestic'\n\n    ctx.log('[Script] FINAL')\n    return 'Others'\n"
rule-providers:
    AdBlock: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/AdBlock.yaml', path: ./Rules/AdBlock, interval: 86400 }
    HTTPDNS: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/HTTPDNS.yaml', path: ./Rules/HTTPDNS, interval: 86400 }
    Special: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Special.yaml', path: ./Rules/Special, interval: 86400 }
    PROXY: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Proxy.yaml', path: ./Rules/Proxy, interval: 86400 }
    Domestic: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Domestic.yaml', path: ./Rules/Domestic, interval: 86400 }
    'Domestic IPs': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Domestic%20IPs.yaml', path: ./Rules/Domestic_IPs, interval: 86400 }
    LAN: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/LAN.yaml', path: ./Rules/LAN, interval: 86400 }
    Netflix: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Netflix.yaml', path: ./Rules/Media/Netflix, interval: 86400 }
    Spotify: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Spotify.yaml', path: ./Rules/Media/Spotify, interval: 86400 }
    YouTube: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/YouTube.yaml', path: ./Rules/Media/YouTube, interval: 86400 }
    Max: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Max.yaml', path: ./Rules/Media/Max, interval: 86400 }
    Bilibili: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Bilibili.yaml', path: ./Rules/Media/Bilibili, interval: 86400 }
    IQ: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/IQ.yaml', path: ./Rules/Media/IQI, interval: 86400 }
    IQIYI: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/IQIYI.yaml', path: ./Rules/Media/IQYI, interval: 86400 }
    Letv: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Letv.yaml', path: ./Rules/Media/Letv, interval: 86400 }
    'Netease Music': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Netease%20Music.yaml', path: ./Rules/Media/Netease_Music, interval: 86400 }
    'Tencent Video': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Tencent%20Video.yaml', path: ./Rules/Media/Tencent_Video, interval: 86400 }
    Youku: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Youku.yaml', path: ./Rules/Media/Youku, interval: 86400 }
    WeTV: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/WeTV.yaml', path: ./Rules/Media/WeTV, interval: 86400 }
    ABC: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/ABC.yaml', path: ./Rules/Media/ABC, interval: 86400 }
    'Abema TV': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Abema%20TV.yaml', path: ./Rules/Media/Abema_TV, interval: 86400 }
    Amazon: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Amazon.yaml', path: ./Rules/Media/Amazon, interval: 86400 }
    'Apple Music': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Apple%20Music.yaml', path: ./Rules/Media/Apple_Music, interval: 86400 }
    'Apple News': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Apple%20News.yaml', path: ./Rules/Media/Apple_News, interval: 86400 }
    'Apple TV': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Apple%20TV.yaml', path: ./Rules/Media/Apple_TV, interval: 86400 }
    Bahamut: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Bahamut.yaml', path: ./Rules/Media/Bahamut, interval: 86400 }
    'BBC iPlayer': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/BBC%20iPlayer.yaml', path: ./Rules/Media/BBC_iPlayer, interval: 86400 }
    DAZN: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/DAZN.yaml', path: ./Rules/Media/DAZN, interval: 86400 }
    'Discovery Plus': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Discovery%20Plus.yaml', path: ./Rules/Media/Discovery_Plus, interval: 86400 }
    'Disney Plus': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Disney%20Plus.yaml', path: ./Rules/Media/Disney_Plus, interval: 86400 }
    DMM: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/DMM.yaml', path: ./Rules/Media/DMM, interval: 86400 }
    encoreTVB: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/encoreTVB.yaml', path: ./Rules/Media/encoreTVB, interval: 86400 }
    'F1 TV': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/F1%20TV.yaml', path: ./Rules/Media/F1_TV, interval: 86400 }
    'Fox Now': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Fox%20Now.yaml', path: ./Rules/Media/Fox_Now, interval: 86400 }
    Fox+: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Fox%2B.yaml', path: ./Rules/Media/Fox+, interval: 86400 }
    'Hulu Japan': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Hulu%20Japan.yaml', path: ./Rules/Media/Hulu_Japan, interval: 86400 }
    Hulu: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Hulu.yaml', path: ./Rules/Media/Hulu, interval: 86400 }
    Japonx: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Japonx.yaml', path: ./Rules/Media/Japonx, interval: 86400 }
    JOOX: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/JOOX.yaml', path: ./Rules/Media/JOOX, interval: 86400 }
    KKBOX: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/KKBOX.yaml', path: ./Rules/Media/KKBOX, interval: 86400 }
    KKTV: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/KKTV.yaml', path: ./Rules/Media/KKTV, interval: 86400 }
    'Line TV': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Line%20TV.yaml', path: ./Rules/Media/Line_TV, interval: 86400 }
    'myTV SUPER': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/myTV%20SUPER.yaml', path: ./Rules/Media/myTV_SUPER, interval: 86400 }
    Niconico: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Niconico.yaml', path: ./Rules/Media/Niconico, interval: 86400 }
    Pandora: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Pandora.yaml', path: ./Rules/Media/Pandora, interval: 86400 }
    PBS: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/PBS.yaml', path: ./Rules/Media/PBS, interval: 86400 }
    Pornhub: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Pornhub.yaml', path: ./Rules/Media/Pornhub, interval: 86400 }
    Soundcloud: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/Soundcloud.yaml', path: ./Rules/Media/Soundcloud, interval: 86400 }
    ViuTV: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Media/ViuTV.yaml', path: ./Rules/Media/ViuTV, interval: 86400 }
    Telegram: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Telegram.yaml', path: ./Rules/Telegram, interval: 86400 }
    Crypto: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Crypto.yaml', path: ./Rules/Crypto, interval: 86400 }
    Discord: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Discord.yaml', path: ./Rules/Discord, interval: 86400 }
    Steam: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Steam.yaml', path: ./Rules/Steam, interval: 86400 }
    TikTok: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/TikTok.yaml', path: ./Rules/TikTok, interval: 86400 }
    Speedtest: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Speedtest.yaml', path: ./Rules/Speedtest, interval: 86400 }
    PayPal: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/PayPal.yaml', path: ./Rules/PayPal, interval: 86400 }
    Microsoft: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Microsoft.yaml', path: ./Rules/Microsoft, interval: 86400 }
    'AI Suite': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/AI%20Suite.yaml', path: './Rules/AI Suite', interval: 86400 }
    Apple: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Apple.yaml', path: ./Rules/Apple, interval: 86400 }
    'Google FCM': { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Google%20FCM.yaml', path: './Rules/Google FCM', interval: 86400 }
    Scholar: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/Scholar.yaml', path: ./Rules/Scholar, interval: 86400 }
    miHoYo: { type: http, behavior: classical, url: 'https://raw.dler.io/dler-io/Rules/main/Clash/Provider/miHoYo.yaml', path: ./Rules/miHoYo, interval: 86400 }
