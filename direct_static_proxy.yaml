port: 8888
socks-port: 8889
mixed-port: 8899
allow-lan: false
mode: Rule
log-level: info
external-controller: '127.0.0.1:6170'
secret: dler

proxies:
  - name: "美国静态IP直连"
    type: socks5
    server: ************
    port: 12324
    username: 14af6324f90cb
    password: cd36401fbd
    udp: true

proxy-groups:
  - name: Proxy
    type: select
    proxies:
      - 美国静态IP直连
      - DIRECT

  - name: Domestic
    type: select
    proxies:
      - DIRECT
      - 美国静态IP直连

rules:
  # 本地网络直连
  - IP-CIDR,***********/16,DIRECT
  - IP-CIDR,10.0.0.0/8,DIRECT
  - IP-CIDR,**********/12,DIRECT
  - IP-CIDR,*********/8,DIRECT
  - IP-CIDR,**********/10,DIRECT
  - IP-CIDR,*********/4,DIRECT
  - IP-CIDR,fe80::/10,DIRECT

  # 直连规则 - 中国大陆网站
  - DOMAIN-SUFFIX,cn,DIRECT
  - DOMAIN-SUFFIX,com.cn,DIRECT
  - DOMAIN-SUFFIX,baidu.com,DIRECT
  - DOMAIN-SUFFIX,qq.com,DIRECT
  - DOMAIN-SUFFIX,taobao.com,DIRECT
  - DOMAIN-SUFFIX,tmall.com,DIRECT
  - DOMAIN-SUFFIX,jd.com,DIRECT
  - DOMAIN-SUFFIX,weibo.com,DIRECT
  - DOMAIN-SUFFIX,zhihu.com,DIRECT
  - DOMAIN-SUFFIX,bilibili.com,DIRECT

  # 中国大陆IP直连
  - GEOIP,CN,DIRECT

  # 所有其他流量都走美国代理（包括IP查询网站）
  - MATCH,Proxy
