payload:
  # > Google FCM
  # According to Firebase FAQ https://firebase.google.com/docs/cloud-messaging/concept-options#messaging-ports-and-your-firewall
  - DOMAIN,mtalk.google.com
  - DOMAIN,mtalk4.google.com
  - DOMAIN,mtalk-staging.google.com
  - DOMAIN,mtalk-dev.google.com
  - DOMAIN,alt1-mtalk.google.com
  - DOMAIN,alt2-mtalk.google.com
  - DOMAIN,alt3-mtalk.google.com
  - DOMAIN,alt4-mtalk.google.com
  - DOMAIN,alt5-mtalk.google.com
  - DOMAIN,alt6-mtalk.google.com
  - DOMAIN,alt7-mtalk.google.com
  - DOMAIN,alt8-mtalk.google.com
  - DOMAIN,android.apis.google.com
  - DOMAIN,device-provisioning.googleapis.com
  - DOMAIN,firebaseinstallations.googleapis.com
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,*************/32,no-resolve
  - IP-CIDR,*************/32,no-resolve
  - IP-CIDR,*************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,***************/32,no-resolve
  - IP-CIDR,*************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,***************/32,no-resolve
  - IP-CIDR,***************/32,no-resolve
  - IP-CIDR,***************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
  - IP-CIDR,***************/32,no-resolve
  - IP-CIDR,***************/32,no-resolve
  - IP-CIDR,***************/32,no-resolve
  - IP-CIDR,**************/32,no-resolve
